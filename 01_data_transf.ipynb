{"cells": [{"cell_type": "markdown", "id": "633bfa0b", "metadata": {}, "source": ["## Initial Transformation and Data Cleaning"]}, {"cell_type": "code", "execution_count": 1, "id": "05d936fd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully\n"]}], "source": ["import pandas as pd\n", "\n", "# Importing custom functions \n", "import useful_functions as uf\n", "\n", "print(\"Libraries imported successfully\")"]}, {"cell_type": "code", "execution_count": 2, "id": "0f5bbb3b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "First few rows of the DataFrame:\n", "           ssn               cc_num    first       last gender  \\\n", "0  ***********        *************    <PERSON>   \n", "1  ***********  4351161559407816183   <PERSON>   \n", "2  ***********        *************  <PERSON>   \n", "3  ***********     4238849696532874  <PERSON>   \n", "4  ***********     4514627048281480     <PERSON>   \n", "\n", "                       street           city state    zip      lat      long  \\\n", "0        863 Lawrence Valleys  Staten Island    NY  10302  40.6306  -74.1379   \n", "1  310 Kendra Common Apt. 164        Peabody    MA   1960  42.5326  -70.9612   \n", "2            05641 Robin Port       Waukomis    OK  73773  36.2781  -97.8996   \n", "3      26916 Carlson Mountain    Los Angeles    CA  90019  34.0482 -118.3343   \n", "4             809 Burns Creek         Austin    TX  78727  30.4254  -97.7195   \n", "\n", "   city_pop                                    job         dob      acct_num  \\\n", "0    468730                  Accounting technician  1982-10-03  ************   \n", "1     50944                     Professor Emeritus  1994-06-07  ************   \n", "2      1744   International aid/development worker  1934-05-30  ************   \n", "3   2383912                    Seismic interpreter  1991-12-26  ************   \n", "4    940359  Medical laboratory scientific officer  1998-05-22  ************   \n", "\n", "                         profile  \n", "0  adults_2550_female_urban.json  \n", "1  adults_2550_female_urban.json  \n", "2  adults_50up_female_rural.json  \n", "3    adults_2550_male_urban.json  \n", "4  adults_2550_female_urban.json  \n", "\n", "DataFrame columns:\n", "['ssn', 'cc_num', 'first', 'last', 'gender', 'street', 'city', 'state', 'zip', 'lat', 'long', 'city_pop', 'job', 'dob', 'acct_num', 'profile']\n", "\n", "DataFrame shape: (1010, 16)\n", "\n", "DataFrame info:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 1010 entries, 0 to 1009\n", "Data columns (total 16 columns):\n", " #   Column    Non-Null Count  Dtype  \n", "---  ------    --------------  -----  \n", " 0   ssn       1010 non-null   object \n", " 1   cc_num    1010 non-null   int64  \n", " 2   first     1010 non-null   object \n", " 3   last      1010 non-null   object \n", " 4   gender    1010 non-null   object \n", " 5   street    1010 non-null   object \n", " 6   city      1010 non-null   object \n", " 7   state     1010 non-null   object \n", " 8   zip       1010 non-null   int64  \n", " 9   lat       1010 non-null   float64\n", " 10  long      1010 non-null   float64\n", " 11  city_pop  1010 non-null   int64  \n", " 12  job       1010 non-null   object \n", " 13  dob       1010 non-null   object \n", " 14  acct_num  1010 non-null   int64  \n", " 15  profile   1010 non-null   object \n", "dtypes: float64(2), int64(4), object(10)\n", "memory usage: 645.4 KB\n", "None\n"]}], "source": ["# Load customer data(using relative path)\n", "customers_df = pd.read_csv('data/customers.csv', delimiter='|')\n", "uf.show_df_info(customers_df)"]}, {"cell_type": "code", "execution_count": 3, "id": "ac5634ef", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>adults_2550</td>\n", "      <td>female</td>\n", "      <td>urban.json</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>adults_2550</td>\n", "      <td>female</td>\n", "      <td>urban.json</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>adults_50up</td>\n", "      <td>female</td>\n", "      <td>rural.json</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>adults_2550</td>\n", "      <td>male</td>\n", "      <td>urban.json</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>adults_2550</td>\n", "      <td>female</td>\n", "      <td>urban.json</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             0       1           2\n", "0  adults_2550  female  urban.json\n", "1  adults_2550  female  urban.json\n", "2  adults_50up  female  rural.json\n", "3  adults_2550    male  urban.json\n", "4  adults_2550  female  urban.json"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Splitting the profile column into multiple columns\n", "# https://pandas.pydata.org/docs/reference/api/pandas.Series.str.rsplit.html\n", "\n", "profile_parts = customers_df['profile'].str.rsplit('_', n=2, expand=True)\n", "profile_parts.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "539343be", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ssn</th>\n", "      <th>cc_num</th>\n", "      <th>first</th>\n", "      <th>last</th>\n", "      <th>gender</th>\n", "      <th>street</th>\n", "      <th>city</th>\n", "      <th>state</th>\n", "      <th>zip</th>\n", "      <th>lat</th>\n", "      <th>long</th>\n", "      <th>city_pop</th>\n", "      <th>job</th>\n", "      <th>dob</th>\n", "      <th>acct_num</th>\n", "      <th>pop_group</th>\n", "      <th>location</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>***********</td>\n", "      <td>*************</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>F</td>\n", "      <td>863 Lawrence Valleys</td>\n", "      <td>Staten Island</td>\n", "      <td>NY</td>\n", "      <td>10302</td>\n", "      <td>40.6306</td>\n", "      <td>-74.1379</td>\n", "      <td>468730</td>\n", "      <td>Accounting technician</td>\n", "      <td>1982-10-03</td>\n", "      <td>************</td>\n", "      <td>adults_2550</td>\n", "      <td>urban</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>***********</td>\n", "      <td>4351161559407816183</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>F</td>\n", "      <td>310 Kendra Common Apt. 164</td>\n", "      <td>Peabody</td>\n", "      <td>MA</td>\n", "      <td>1960</td>\n", "      <td>42.5326</td>\n", "      <td>-70.9612</td>\n", "      <td>50944</td>\n", "      <td>Professor Emeritus</td>\n", "      <td>1994-06-07</td>\n", "      <td>************</td>\n", "      <td>adults_2550</td>\n", "      <td>urban</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>***********</td>\n", "      <td>*************</td>\n", "      <td>Melinda</td>\n", "      <td><PERSON></td>\n", "      <td>F</td>\n", "      <td>05641 Robin Port</td>\n", "      <td>W<PERSON><PERSON><PERSON></td>\n", "      <td>OK</td>\n", "      <td>73773</td>\n", "      <td>36.2781</td>\n", "      <td>-97.8996</td>\n", "      <td>1744</td>\n", "      <td>International aid/development worker</td>\n", "      <td>1934-05-30</td>\n", "      <td>************</td>\n", "      <td>adults_50up</td>\n", "      <td>rural</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>***********</td>\n", "      <td>4238849696532874</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>M</td>\n", "      <td>26916 Carlson Mountain</td>\n", "      <td>Los Angeles</td>\n", "      <td>CA</td>\n", "      <td>90019</td>\n", "      <td>34.0482</td>\n", "      <td>-118.3343</td>\n", "      <td>2383912</td>\n", "      <td>Seismic interpreter</td>\n", "      <td>1991-12-26</td>\n", "      <td>************</td>\n", "      <td>adults_2550</td>\n", "      <td>urban</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>***********</td>\n", "      <td>4514627048281480</td>\n", "      <td>Lisa</td>\n", "      <td><PERSON></td>\n", "      <td>F</td>\n", "      <td>809 Burns Creek</td>\n", "      <td>Austin</td>\n", "      <td>TX</td>\n", "      <td>78727</td>\n", "      <td>30.4254</td>\n", "      <td>-97.7195</td>\n", "      <td>940359</td>\n", "      <td>Medical laboratory scientific officer</td>\n", "      <td>1998-05-22</td>\n", "      <td>************</td>\n", "      <td>adults_2550</td>\n", "      <td>urban</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           ssn               cc_num    first       last gender  \\\n", "0  ***********        *************    <PERSON>   \n", "1  ***********  4351161559407816183   <PERSON>   \n", "2  ***********        *************  <PERSON>   \n", "3  ***********     4238849696532874  <PERSON>   \n", "4  ***********     4514627048281480     <PERSON>   \n", "\n", "                       street           city state    zip      lat      long  \\\n", "0        863 Lawrence Valleys  Staten Island    NY  10302  40.6306  -74.1379   \n", "1  310 Kendra Common Apt. 164        Peabody    MA   1960  42.5326  -70.9612   \n", "2            05641 Robin Port       Waukomis    OK  73773  36.2781  -97.8996   \n", "3      26916 Carlson Mountain    Los Angeles    CA  90019  34.0482 -118.3343   \n", "4             809 Burns Creek         Austin    TX  78727  30.4254  -97.7195   \n", "\n", "   city_pop                                    job         dob      acct_num  \\\n", "0    468730                  Accounting technician  1982-10-03  ************   \n", "1     50944                     Professor Emeritus  1994-06-07  ************   \n", "2      1744   International aid/development worker  1934-05-30  ************   \n", "3   2383912                    Seismic interpreter  1991-12-26  ************   \n", "4    940359  Medical laboratory scientific officer  1998-05-22  ************   \n", "\n", "     pop_group location  \n", "0  adults_2550    urban  \n", "1  adults_2550    urban  \n", "2  adults_50up    rural  \n", "3  adults_2550    urban  \n", "4  adults_2550    urban  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Leveraging the information from the profile column to create new columns(Feature Engineering)\n", "\n", "customers_df['pop_group'] = profile_parts[0]\n", "customers_df['location'] = profile_parts[2].str.split('.').str[0] # Remove .json extension\n", "\n", "# Drop the original profile column\n", "customers_df.drop(columns=['profile'], inplace=True)\n", "customers_df.head()"]}, {"cell_type": "markdown", "id": "974cf5a2", "metadata": {}, "source": ["####  Planning to use as less memory as possible, some of the data types will be optimized"]}, {"cell_type": "code", "execution_count": 5, "id": "9827c432", "metadata": {}, "outputs": [{"data": {"text/plain": ["gender         2\n", "state         50\n", "job          492\n", "pop_group      3\n", "location       2\n", "dtype: int64"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Display unique values in columns that might be used as categorical\n", "customers_df[['gender', 'state', 'job', 'pop_group','location']].nunique()\n"]}, {"cell_type": "code", "execution_count": 6, "id": "2759a2b6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Recommended type for city_pop: uint32\n"]}], "source": ["# Check what type city_pop should use\n", "city_pop_recommended_type = uf.get_safe_int_type(customers_df['city_pop'])\n", "print(f\"Recommended type for city_pop: {city_pop_recommended_type}\")"]}, {"cell_type": "code", "execution_count": 7, "id": "58cd07f1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Before optimization:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 1010 entries, 0 to 1009\n", "Data columns (total 17 columns):\n", " #   Column     Non-Null Count  Dtype  \n", "---  ------     --------------  -----  \n", " 0   ssn        1010 non-null   object \n", " 1   cc_num     1010 non-null   int64  \n", " 2   first      1010 non-null   object \n", " 3   last       1010 non-null   object \n", " 4   gender     1010 non-null   object \n", " 5   street     1010 non-null   object \n", " 6   city       1010 non-null   object \n", " 7   state      1010 non-null   object \n", " 8   zip        1010 non-null   int64  \n", " 9   lat        1010 non-null   float64\n", " 10  long       1010 non-null   float64\n", " 11  city_pop   1010 non-null   int64  \n", " 12  job        1010 non-null   object \n", " 13  dob        1010 non-null   object \n", " 14  acct_num   1010 non-null   int64  \n", " 15  pop_group  1010 non-null   object \n", " 16  location   1010 non-null   object \n", "dtypes: float64(2), int64(4), object(11)\n", "memory usage: 682.0 KB\n", "\n", "After optimization:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 1010 entries, 0 to 1009\n", "Data columns (total 17 columns):\n", " #   Column     Non-Null Count  Dtype   \n", "---  ------     --------------  -----   \n", " 0   ssn        1010 non-null   string  \n", " 1   cc_num     1010 non-null   string  \n", " 2   first      1010 non-null   string  \n", " 3   last       1010 non-null   string  \n", " 4   gender     1010 non-null   category\n", " 5   street     1010 non-null   string  \n", " 6   city       1010 non-null   string  \n", " 7   state      1010 non-null   category\n", " 8   zip        1010 non-null   string  \n", " 9   lat        1010 non-null   float32 \n", " 10  long       1010 non-null   float32 \n", " 11  city_pop   1010 non-null   uint32  \n", " 12  job        1010 non-null   string  \n", " 13  dob        1010 non-null   string  \n", " 14  acct_num   1010 non-null   string  \n", " 15  pop_group  1010 non-null   category\n", " 16  location   1010 non-null   category\n", "dtypes: category(4), float32(2), string(10), uint32(1)\n", "memory usage: 620.2 KB\n"]}], "source": ["# Before optimization\n", "print(\"Before optimization:\")\n", "old_mem_usage = customers_df.memory_usage(deep=True).sum() / 1024\n", "customers_df.info(memory_usage='deep')\n", "\n", "# Apply optimization\n", "new_customer_types = {\n", "    'category': ['gender', 'state', 'pop_group', 'location'],\n", "    'string': ['ssn', 'cc_num', 'first', 'last', 'street', 'city', 'dob', 'job', 'zip', 'acct_num'],\n", "    city_pop_recommended_type: ['city_pop'],\n", "    'float32': ['lat', 'long'] # Acceptable loss for fraud detection. It might need to be float64 for other applications\n", "}\n", "# Replacing customers_df with the optimized version to save memory\n", "customers_df = uf.optimize_df_types(customers_df, new_customer_types)\n", "\n", "# After optimization  \n", "\n", "new_mem_usage = customers_df.memory_usage(deep=True).sum() / 1024\n", "print(\"\\nAfter optimization:\")\n", "customers_df.info(memory_usage='deep')"]}, {"cell_type": "code", "execution_count": 8, "id": "cb10635b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original memory usage: 681.99 KB, optimized memory usage: 620.15 KB\n", "\n", "Memory usage reduction: 9.07%\n"]}], "source": ["# Compare memory usage\n", "\n", "print(f\"Original memory usage: {old_mem_usage:,.2f} KB, optimized memory usage: {new_mem_usage:,.2f} KB\")\n", "\n", "print(f\"\\nMemory usage reduction: {100*(old_mem_usage - new_mem_usage)/old_mem_usage:.2f}%\")"]}, {"cell_type": "markdown", "id": "3bcc4f89", "metadata": {}, "source": ["##### Not a huge saving, but every bit helps! This principle might be useful when working with larger datasets."]}, {"cell_type": "markdown", "id": "657ffa69", "metadata": {}, "source": ["#### As expected, the majority of the customers are from urban areas. Adults over 50 are the majority on our small customer dataset."]}, {"cell_type": "markdown", "id": "d8f3277a", "metadata": {}, "source": ["### We will work now with the transaction files"]}, {"cell_type": "code", "execution_count": 9, "id": "232f1f61", "metadata": {}, "outputs": [], "source": ["# The transaction data is split across multiple files, so we need to load them all and concatenate them into a single DataFrame\n", "transaction_files = uf.get_files_dir('data', file_mask='*.csv')\n", "transaction_files = [f for f in transaction_files if 'customers.csv' not in f] # Exclude customers.csv"]}, {"cell_type": "code", "execution_count": 10, "id": "3c6c7a8e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "First few rows of the DataFrame:\n", "           ssn                         trans_num  trans_date trans_time  \\\n", "0  ***********  87a777b822486a650c6323a7bb15b471  2023-12-21   00:28:10   \n", "1  ***********  29064f0b63a35ed0f2226eccc24b6c06  2023-12-21   00:26:42   \n", "2  ***********  37a4019328a40f02d7f6c4ffffc06f5f  2023-12-21   00:16:01   \n", "3  ***********  42a3aa638e84935ce525384cbff28753  2023-12-21   02:22:31   \n", "4  ***********  5a788925818de8f0e5ad09b3d30c0cfd  2023-12-21   00:50:39   \n", "\n", "    unix_time       category     amt  is_fraud  \\\n", "0  1703136490       misc_pos    8.05         1   \n", "1  1703136402  gas_transport  361.70         1   \n", "2  1703135761       misc_pos  345.33         1   \n", "3  1703143351       misc_pos  817.02         1   \n", "4  1703137839    grocery_pos  777.19         1   \n", "\n", "                         merchant  merch_lat  merch_long  \n", "0             fraud_<PERSON><PERSON><PERSON>-Upton  41.219555  -73.327258  \n", "1               fraud_Huels-<PERSON>  40.418529  -73.293731  \n", "2         fraud_<PERSON><PERSON><PERSON><PERSON>-<PERSON>ask<PERSON><PERSON>  42.278211  -74.450847  \n", "3               fraud_Haley Group  42.106140  -72.768034  \n", "4  fraud_<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>  40.361006  -73.579606  \n", "\n", "DataFrame columns:\n", "['ssn', 'trans_num', 'trans_date', 'trans_time', 'unix_time', 'category', 'amt', 'is_fraud', 'merchant', 'merch_lat', 'merch_long']\n", "\n", "DataFrame shape: (18055, 11)\n", "\n", "DataFrame info:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 18055 entries, 0 to 18054\n", "Data columns (total 11 columns):\n", " #   Column      Non-Null Count  Dtype  \n", "---  ------      --------------  -----  \n", " 0   ssn         18055 non-null  object \n", " 1   trans_num   18055 non-null  object \n", " 2   trans_date  18055 non-null  object \n", " 3   trans_time  18055 non-null  object \n", " 4   unix_time   18055 non-null  int64  \n", " 5   category    18055 non-null  object \n", " 6   amt         18055 non-null  float64\n", " 7   is_fraud    18055 non-null  int64  \n", " 8   merchant    18055 non-null  object \n", " 9   merch_lat   18055 non-null  float64\n", " 10  merch_long  18055 non-null  float64\n", "dtypes: float64(3), int64(2), object(6)\n", "memory usage: 7.4 MB\n", "None\n"]}], "source": ["sample_transaction_file = transaction_files[0]\n", "sample_transactions_df = pd.read_csv(sample_transaction_file, delimiter='|')\n", "uf.show_df_info(sample_transactions_df)"]}, {"cell_type": "markdown", "id": "3f83c225", "metadata": {}, "source": ["#### Unix time\n", "##### https://en.wikipedia.org/wiki/Unix_time"]}, {"cell_type": "code", "execution_count": 11, "id": "32f6c858", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing file 1/53: data\\adults_2550_female_rural_0000-0201.csv\n", "Processed 18,055 rows so far...\n", "Processing file 2/53: data\\adults_2550_female_rural_0404-0605.csv\n", "Processing file 3/53: data\\adults_2550_female_rural_0606-0807.csv\n", "Processing file 4/53: data\\adults_2550_female_rural_0808-1009.csv\n", "Processing file 5/53: data\\adults_2550_female_urban_0000-0201.csv\n", "Processing file 6/53: data\\adults_2550_female_urban_0202-0403.csv\n", "Processing file 7/53: data\\adults_2550_female_urban_0404-0605.csv\n", "Processing file 8/53: data\\adults_2550_female_urban_0606-0807.csv\n", "Processing file 9/53: data\\adults_2550_female_urban_0808-1009.csv\n", "Processing file 10/53: data\\adults_2550_male_rural_0000-0201.csv\n", "Processing file 11/53: data\\adults_2550_male_rural_0202-0403.csv\n", "Processed 1,268,446 rows so far...\n", "Processing file 12/53: data\\adults_2550_male_rural_0404-0605.csv\n", "Processing file 13/53: data\\adults_2550_male_rural_0606-0807.csv\n", "Processing file 14/53: data\\adults_2550_male_rural_0808-1009.csv\n", "Processing file 15/53: data\\adults_2550_male_urban_0000-0201.csv\n", "Processing file 16/53: data\\adults_2550_male_urban_0202-0403.csv\n", "Processing file 17/53: data\\adults_2550_male_urban_0404-0605.csv\n", "Processing file 18/53: data\\adults_2550_male_urban_0606-0807.csv\n", "Processing file 19/53: data\\adults_2550_male_urban_0808-1009.csv\n", "Processing file 20/53: data\\adults_50up_female_rural_0000-0201.csv\n", "Processing file 21/53: data\\adults_50up_female_rural_0202-0403.csv\n", "Processed 2,535,065 rows so far...\n", "Processing file 22/53: data\\adults_50up_female_rural_0404-0605.csv\n", "Processing file 23/53: data\\adults_50up_female_rural_0606-0807.csv\n", "Processing file 24/53: data\\adults_50up_female_rural_0808-1009.csv\n", "Processing file 25/53: data\\adults_50up_female_urban_0000-0201.csv\n", "Processing file 26/53: data\\adults_50up_female_urban_0202-0403.csv\n", "Processing file 27/53: data\\adults_50up_female_urban_0404-0605.csv\n", "Processing file 28/53: data\\adults_50up_female_urban_0606-0807.csv\n", "Processing file 29/53: data\\adults_50up_female_urban_0808-1009.csv\n", "Processing file 30/53: data\\adults_50up_male_rural_0202-0403.csv\n", "Processing file 31/53: data\\adults_50up_male_rural_0404-0605.csv\n", "Processed 3,359,995 rows so far...\n", "Processing file 32/53: data\\adults_50up_male_rural_0606-0807.csv\n", "Processing file 33/53: data\\adults_50up_male_rural_0808-1009.csv\n", "Processing file 34/53: data\\adults_50up_male_urban_0000-0201.csv\n", "Processing file 35/53: data\\adults_50up_male_urban_0202-0403.csv\n", "Processing file 36/53: data\\adults_50up_male_urban_0404-0605.csv\n", "Processing file 37/53: data\\adults_50up_male_urban_0606-0807.csv\n", "Processing file 38/53: data\\adults_50up_male_urban_0808-1009.csv\n", "Processing file 39/53: data\\young_adults_female_rural_0606-0807.csv\n", "Processing file 40/53: data\\young_adults_female_rural_0808-1009.csv\n", "Processing file 41/53: data\\young_adults_female_urban_0000-0201.csv\n", "Processed 4,204,734 rows so far...\n", "Processing file 42/53: data\\young_adults_female_urban_0202-0403.csv\n", "Processing file 43/53: data\\young_adults_female_urban_0404-0605.csv\n", "Processing file 44/53: data\\young_adults_female_urban_0606-0807.csv\n", "Processing file 45/53: data\\young_adults_female_urban_0808-1009.csv\n", "Processing file 46/53: data\\young_adults_male_rural_0202-0403.csv\n", "Processing file 47/53: data\\young_adults_male_rural_0606-0807.csv\n", "Processing file 48/53: data\\young_adults_male_rural_0808-1009.csv\n", "Processing file 49/53: data\\young_adults_male_urban_0000-0201.csv\n", "Processing file 50/53: data\\young_adults_male_urban_0202-0403.csv\n", "Processing file 51/53: data\\young_adults_male_urban_0404-0605.csv\n", "Processed 4,601,685 rows so far...\n", "Processing file 52/53: data\\young_adults_male_urban_0606-0807.csv\n", "Processing file 53/53: data\\young_adults_male_urban_0808-1009.csv\n", "Merging all optimized files...\n"]}], "source": ["# Working with the transaction data \n", "# Optimizing types\n", "transaction_types = {\n", "    'category': ['category'],\n", "    'uint32': ['unix_time'], # uint32 max value is 2,147,483,647, which is more than enough for our purposes(until 2038, at least). \n", "    'float32': ['amt', 'merch_lat', 'merch_long'],\n", "    'uint8': ['is_fraud'],\n", "    'string': ['ssn', 'trans_num', 'trans_date', 'trans_time', 'merchant']\n", "}\n", "# Process and merge all transaction files\n", "transactions_df = uf.process_and_merge_files(transaction_files, transaction_types)"]}, {"cell_type": "code", "execution_count": 12, "id": "f1451cd2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "First few rows of the DataFrame:\n", "           ssn                         trans_num  trans_date trans_time  \\\n", "0  ***********  87a777b822486a650c6323a7bb15b471  2023-12-21   00:28:10   \n", "1  ***********  29064f0b63a35ed0f2226eccc24b6c06  2023-12-21   00:26:42   \n", "2  ***********  37a4019328a40f02d7f6c4ffffc06f5f  2023-12-21   00:16:01   \n", "3  ***********  42a3aa638e84935ce525384cbff28753  2023-12-21   02:22:31   \n", "4  ***********  5a788925818de8f0e5ad09b3d30c0cfd  2023-12-21   00:50:39   \n", "\n", "    unix_time       category         amt  is_fraud  \\\n", "0  1703136490       misc_pos    8.050000         1   \n", "1  1703136402  gas_transport  361.700012         1   \n", "2  1703135761       misc_pos  345.329987         1   \n", "3  1703143351       misc_pos  817.020020         1   \n", "4  1703137839    grocery_pos  777.190002         1   \n", "\n", "                         merchant  merch_lat  merch_long  \n", "0             fraud_<PERSON><PERSON><PERSON>-Upton  41.219555  -73.327255  \n", "1               fraud_<PERSON>els-Nolan  40.418530  -73.293732  \n", "2         fraud_<PERSON><PERSON><PERSON><PERSON>-Jask<PERSON>ki  42.278210  -74.450844  \n", "3               fraud_Haley Group  42.106140  -72.768036  \n", "4  fraud_<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>  40.361008  -73.579605  \n", "\n", "DataFrame columns:\n", "['ssn', 'trans_num', 'trans_date', 'trans_time', 'unix_time', 'category', 'amt', 'is_fraud', 'merchant', 'merch_lat', 'merch_long']\n", "\n", "DataFrame shape: (4740009, 11)\n", "\n", "DataFrame info:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 4740009 entries, 0 to 4740008\n", "Data columns (total 11 columns):\n", " #   Column      Dtype   \n", "---  ------      -----   \n", " 0   ssn         string  \n", " 1   trans_num   string  \n", " 2   trans_date  string  \n", " 3   trans_time  string  \n", " 4   unix_time   uint32  \n", " 5   category    category\n", " 6   amt         float32 \n", " 7   is_fraud    uint8   \n", " 8   merchant    string  \n", " 9   merch_lat   float32 \n", " 10  merch_long  float32 \n", "dtypes: category(1), float32(3), string(5), uint32(1), uint8(1)\n", "memory usage: 1.5 GB\n", "None\n"]}], "source": ["# Display the final merged DataFrame\n", "uf.show_df_info(transactions_df)"]}, {"cell_type": "code", "execution_count": 13, "id": "873a84ef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on function get_files_dir in module useful_functions:\n", "\n", "get_files_dir(directory_path: str, file_mask: str = '*.csv') -> list\n", "    Get all files matching the pattern in a directory.\n", "\n", "    Args:\n", "        directory_path: Path to the directory containing files\n", "        file_mask: File pattern to match (default: '*.csv')\n", "\n", "    Returns:\n", "        list: List of file paths matching the pattern\n", "\n"]}], "source": ["help(uf.get_files_dir)"]}, {"cell_type": "code", "execution_count": 14, "id": "ec053533", "metadata": {}, "outputs": [], "source": ["# 1. <PERSON><PERSON><PERSON> primary key candidates\n", "customer_pk_check = uf.check_primary_key_candidates(customers_df, ['ssn', 'cc_num'])\n", "transaction_pk_check = uf.check_primary_key_candidates(transactions_df, ['trans_num'])"]}, {"cell_type": "code", "execution_count": 15, "id": "5b8eeaaf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Customer Primary Key Analysis:\n", "\n", "Primary Key Analysis for 'ssn':\n", "  Total rows: 1,010\n", "  Unique values: 1,010\n", "  Null values: 0\n", "  Duplicate values: 0\n", "  Status: VALID_PRIMARY_KEY\n", "\n", "Primary Key Analysis for 'cc_num':\n", "  Total rows: 1,010\n", "  Unique values: 1,010\n", "  Null values: 0\n", "  Duplicate values: 0\n", "  Status: VALID_PRIMARY_KEY\n", "\n", "Transaction Primary Key Analysis:\n", "\n", "Primary Key Analysis for 'trans_num':\n", "  Total rows: 4,740,009\n", "  Unique values: 4,740,009\n", "  Null values: 0\n", "  Duplicate values: 0\n", "  Status: VALID_PRIMARY_KEY\n"]}], "source": ["print(\"Customer Primary Key Analysis:\")\n", "uf.display_primary_key_analysis( customer_pk_check)\n", "print(\"\\nTransaction Primary Key Analysis:\")\n", "uf.display_primary_key_analysis( transaction_pk_check)"]}, {"cell_type": "code", "execution_count": null, "id": "3796f5bc", "metadata": {}, "outputs": [], "source": ["# Create database\n", "# Remove the comment to create the database\n", "#uf.create_fraud_detection_db(customers_df, transactions_df)"]}, {"cell_type": "code", "execution_count": 18, "id": "a3688e45", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Current memory usage:\n", "Customers: 0.61 MB\n", "Transactions: 1569.09 MB\n", "Sample: 7.38 MB\n", "Total: 1577.08 MB\n", "\n", "Freed 1577.08 MB of memory\n"]}], "source": ["# Memory cleanup\n", "import gc\n", "\n", "# Show current memory usage\n", "customers_memory_mb = customers_df.memory_usage(deep=True).sum() / (1024 * 1024)\n", "transactions_memory_mb = transactions_df.memory_usage(deep=True).sum() / (1024 * 1024)\n", "sample_memory_mb = sample_transactions_df.memory_usage(deep=True).sum() / (1024 * 1024)\n", "total_memory_mb = customers_memory_mb + transactions_memory_mb + sample_memory_mb\n", "\n", "print(f\"Current memory usage:\")\n", "print(f\"Customers: {customers_memory_mb:.2f} MB\")\n", "print(f\"Transactions: {transactions_memory_mb:.2f} MB\")\n", "print(f\"Sample: {sample_memory_mb:.2f} MB\")\n", "print(f\"Total: {total_memory_mb:.2f} MB\")\n", "\n", "# Clean up variables\n", "del customers_df, transactions_df, sample_transactions_df\n", "del profile_parts, transaction_files, sample_transaction_file\n", "del new_customer_types, transaction_types\n", "del customer_pk_check, transaction_pk_check\n", "del old_mem_usage, new_mem_usage, city_pop_recommended_type\n", "\n", "gc.collect()\n", "print(f\"\\nFreed {total_memory_mb:.2f} MB of memory\")"]}], "metadata": {"kernelspec": {"display_name": "card-transactions-analysis", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}